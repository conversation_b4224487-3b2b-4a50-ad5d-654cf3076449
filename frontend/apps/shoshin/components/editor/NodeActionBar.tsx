"use client"

import { <PERSON>LeftRight, ArrowUpDown, Circle, CircleOff, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react"
import { cn } from "../../lib/utils"
import { Button } from "../ui/button"
import { <PERSON>lt<PERSON>, Too<PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "../ui/tooltip"

interface NodeActionBarProps {
  nodeId: string
  nodeType: string
  isEnabled?: boolean
  hasVerticalPorts?: boolean
  disabled?: boolean
  onToggleEnabled?: (nodeId: string) => void
  onDuplicate?: (nodeId: string) => void
  onTogglePorts?: (nodeId: string) => void
  onDelete?: (nodeId: string) => void
  onSettings?: (nodeId: string) => void
}

export function NodeActionBar({
  nodeId,
  nodeType,
  isEnabled = true,
  hasVerticalPorts = false,
  disabled = false,
  onToggleEnabled,
  onDuplicate,
  onTogglePorts,
  onDelete,
  onSettings,
}: <PERSON>deActionBarProps) {
  const isStarterNode = nodeType === 'start'

  return (
    <TooltipProvider>
      {/* Invisible bridge to maintain hover state */}
      <div className="absolute -right-[72px] top-0 w-[72px] h-full opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

      <div
        className={cn(
          "-right-[72px] absolute top-0",
          "flex flex-col items-center gap-2 p-2",
          "rounded-md border border-gray-200 bg-background shadow-sm dark:border-gray-800",
          "opacity-0 transition-opacity duration-200 group-hover:opacity-100 hover:opacity-100",
          "z-[40]"
        )}
      >
        {/* Toggle Enable/Disable */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (!disabled && onToggleEnabled) {
                  onToggleEnabled(nodeId)
                }
              }}
              className={cn(
                "text-gray-500 h-8 w-8 p-0",
                disabled && "cursor-not-allowed opacity-50"
              )}
              disabled={disabled}
            >
              {isEnabled ? (
                <Circle className="h-4 w-4" />
              ) : (
                <CircleOff className="h-4 w-4" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right">
            {disabled
              ? "Read-only mode"
              : isEnabled
              ? "Disable Node"
              : "Enable Node"}
          </TooltipContent>
        </Tooltip>

        {/* Duplicate Node - Don't show for start nodes */}
        {!isStarterNode && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (!disabled && onDuplicate) {
                    onDuplicate(nodeId)
                  }
                }}
                className={cn(
                  "text-gray-500 h-8 w-8 p-0",
                  disabled && "cursor-not-allowed opacity-50"
                )}
                disabled={disabled}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              {disabled ? "Read-only mode" : "Duplicate Node"}
            </TooltipContent>
          </Tooltip>
        )}

        {/* Toggle Vertical/Horizontal Ports - Coming Soon */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Feature disabled - coming soon
              }}
              className={cn(
                "text-gray-400 h-8 w-8 p-0 cursor-not-allowed opacity-50"
              )}
              disabled={true}
            >
              {hasVerticalPorts ? (
                <ArrowUpDown className="h-4 w-4" />
              ) : (
                <ArrowLeftRight className="h-4 w-4" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right">
            Port Orientation - Coming Soon
          </TooltipContent>
        </Tooltip>

        {/* Delete Node - Don't show for start nodes */}
        {!isStarterNode && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (!disabled && onDelete) {
                    onDelete(nodeId)
                  }
                }}
                className={cn(
                  "text-gray-500 hover:text-red-600 h-8 w-8 p-0",
                  disabled && "cursor-not-allowed opacity-50"
                )}
                disabled={disabled}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              {disabled ? "Read-only mode" : "Delete Node"}
            </TooltipContent>
          </Tooltip>
        )}

        {/* Node Settings */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (!disabled && onSettings) {
                  onSettings(nodeId)
                }
              }}
              className={cn(
                "text-gray-500 h-8 w-8 p-0",
                disabled && "cursor-not-allowed opacity-50"
              )}
              disabled={disabled}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right">
            {disabled ? "Read-only mode" : "Node Settings"}
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  )
}
